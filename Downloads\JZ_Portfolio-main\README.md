# 💼 <PERSON><PERSON><PERSON>'s Portfolio

Welcome to my developer portfolio! This project showcases my skills, experience, and personal projects, built using modern technologies with a strong focus on performance, responsiveness, and clean design.

---

## 🚀 Tech Stack

- ⚛️ **React** — Component-based architecture for scalable UI
- 🧑‍💻 **TypeScript** — Type-safe JavaScript development
- ⚡ **Vite** — Fast build tool and dev server
- 🎨 **Tailwind CSS** — Utility-first CSS framework for rapid styling

---

## 📷 Preview

![Portfolio Preview](./screenshot.png) 

---

## 📌 Features
🔥 Smooth animations and transitions
🧭 Fully responsive across devices
🌙 Dark mode support
📱 Mobile-first design
🧩 Modular and reusable components

---


## 🔧 Setup Instructions

### 1. Clone the Repository

```bash
git clone https://github.com/syedajabeenali/JZ_Portfolio.git
cd JZ_Portfolio
```

### 2. Install Dependencies 

```bash
npm install
```

### 3. Run the app

```bash
npm run dev
```

---





